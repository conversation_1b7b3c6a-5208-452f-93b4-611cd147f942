<?php

namespace App\Helpers;

use App\Models\DocumentTemplate;
use Elasticsearch\ClientBuilder;
use Exception;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use Stancl\Tenancy\Tenancy;

class ElasticHelper
{
    // API client
    private $client;
	private $indices;

    public function __construct()
    {
        // initialize ElasticSearch API client
        $this->client = ClientBuilder::create()->setHosts([
            env('ELASTICSEARCH_HOST', 'localhost:9200')
        ])->build();

		// define indices (templates, documents, examples, articles)
		if (tenancy()->initialized) {
			$this->indices = [
				'template' => 'pravomat-templates-tenant-' . tenancy()->tenant->id . '-' . App::environment(),
			];
		} else {
			$this->indices = [
				'template' => 'pravomat-templates-' . App::environment(),
				'document' => 'pravomat-documents-' . App::environment(),
				'example' => 'pravomat-examples-' . App::environment(),
				'article' => 'pravomat-articles-' . App::environment(),
			];
		}
    }

	public function isRunning(): bool {
		try {
			return $this->client->ping();
		} catch(Exception $e) {
			return false;
		}
	}
    public function getIndices(): array {
        return $this->client->indices()->getSettings();
    }

    public function getMapping(): array {
        return $this->client->indices()->getMapping();
    }

	public function getClient(): \Elasticsearch\Client {
		return $this->client;
	}

	private function fetchMappings($index): array {

		$properties = [
			'id' => [
				'type' => 'text',
				'index' => false
			],
			'title_raw' => [
				'type' => 'text',
				'analyzer' => 'simple'
			],
			'title' => [
				'type' => 'text',
				'analyzer' => 'croatian'
			],
			'tags' => [
				'type' => 'text',
				'analyzer' => 'croatian'
			],
		];

		if ($index === $this->indices['article']) {

			$properties['post_title'] = [
				'type' => 'keyword'
			];

			$properties['post_id'] = [
				'type' => 'keyword'
			];

			$properties['description'] = [
				'type' => 'text'
			];
		}

		return ['properties' => $properties];
	}

    private function fetchSettings(): array {
        return [
            "analysis" => [
                "filter" => [
                    "hr_HR" => [
                        "type" => "hunspell",
                        "language" => "hr_HR"
                    ],
                ],
                "analyzer" => [
                    "croatian" => [
                        "tokenizer" => "standard",
                        "filter" => ["lowercase", "hr_HR"],
                        "char_filter" => [
                            "only_alphanumeric"
                        ]
                    ],
                ],
                "char_filter" => [
                    "only_alphanumeric" => [
                        "type" => "pattern_replace",
                        "pattern" => "[^a-zA-Z\d\s:]",
                        "replacement" => ""
                    ]
                ]
            ]
        ];
    }

	/**
	 * @throws Exception
	 */
	public function createIndex($index): array {
		return $this->client->indices()->create(
			[
				'index' => $index,
				'body' => [
					'mappings' => $this->fetchMappings($index),
					"settings" => $this->fetchSettings()
				]
			]
		);
	}

	public function existsIndex($index): bool {
		return $this->client->indices()->exists(['index' => $index]);
	}


    /** ! Use with caution ! */
    public function deleteIndex($index): array {
        return $this->client->indices()->delete(['index' => $index]);
    }


    /** ! Use with caution - deletes indices! */
    public function restartIndices()
    {
		foreach($this->indices as $_index) {
			if ($this->existsIndex($_index)) {
				$this->deleteIndex($_index);
			}

			$this->createIndex($_index);
		}
    }

    /** Deletes document from Elasticsearch index */
    public function delete($index, $id) {

        $delete_params = [
            'index' => $this->indices[$index],
            'id' => $id,
        ];

        // check if the document exists
        $exists_params = [
            'index' => $this->indices[$index],
            'id' => $id,
        ];

        if ($this->client->exists($exists_params)) {
            return $this->client->delete($delete_params);
        }

        return null;
    }



    /** Document related methods */

    /**
     * @throws Exception
     */
    public function importTemplates()
    {
        $templates = DocumentTemplate::where('is_visible', 1)->get();
        $params = ['body' => []];

        $i = 0;
        foreach($templates as $_template) {

            $params['body'][] = [
                'index' => [
                    '_index' => $this->indices['template'],
                    '_id' => $_template->id
                ]
            ];

            $params['body'][] = [
	            'id' => $_template->id,
                'title' => $_template->public_title,
                'title_raw' => $_template->public_title,
                'tags' => collect(explode(',', $_template->tags))->map(function($el){
                    return trim($el);
                }),
            ];

            // every 10 documents stop and send the bulk request
            if (($i > 0) && ($i % 10 == 0)) {

                $r_result = $this->client->bulk($params);

                if ($r_result["errors"]) {
                    throw new Exception('Error importing templates in ES.');
                }

                // erase the previous bulk request
                $params = ['body' => []];
            }
            $i++;
        }

        // send the remaining batch if it exists
        if (!empty($params['body'])) {
            $result = $this->client->bulk($params);

	        if ($result["errors"]) {
		        throw new Exception('Error importing templates in ES.');
	        }
        }

    }

    /**
	 * @throws Exception
	 */
	public function importDocuments()
	{
		$documents = Post::type('document')->status('publish')->get();
		$params = ['body' => []];

		$i = 0;
		foreach($documents as $_document) {

			$params['body'][] = [
				'index' => [
					'_index' => $this->indices['document'],
					'_id' => $_document->ID
				]
			];

			$params['body'][] = [
				'id' => $_document->ID,
				'title' => $_document->post_title,
				'title_raw' => $_document->post_title,
				'tags' => $_document->acf ? collect(explode(',', $_document->acf->tags))->map(function($el){
                    return trim($el);
                }) : null,
			];

			// every 10 documents stop and send the bulk request
			if (($i > 0) && ($i % 10 == 0)) {

				$r_result = $this->client->bulk($params);

				if ($r_result["errors"]) {
					throw new Exception('Error importing documents in ES.');
				}

				// erase the previous bulk request
				$params = ['body' => []];
			}
			$i++;
		}

		// send the remaining batch if it exists
		if (!empty($params['body'])) {
			$result = $this->client->bulk($params);

			if ($result["errors"]) {
				throw new Exception('Error importing documents in ES.');
			}
		}

	}

    /**
     * @param int $document_id
     * @throws Exception
     */
    public function updateDocument(int $document_id)
    {
        $document = Post::type('document')->find($document_id);

        if (!$document) {
            throw new Exception('Document not found: '.$document_id);
        }

        // delete from elastic
        $this->delete('document', $document->ID);

        // insert if status "publish"
        if ($document->post_status === 'publish') {
            $params = [
                'index' => $this->indices['document'],
                'id' => $document->ID,
                'body' => [
                    'id' => $document->ID,
                    'title' => $document->post_title,
                    'title_raw' => $document->post_title,
                    'tags' => $document->acf ? collect(explode(',', $document->acf->tags))->map(function($el){
                        return trim($el);
                    }) : null,
                ]
            ];

            $result = $this->client->index($params);

            if (isset($result["error"])) {
                throw new Exception('Error updating document in ES: '.$document_id);
            }
        }
    }

    /**
	 * @throws Exception
	 */
	public function importExamples()
	{
		$examples = Post::type('example')->with('acf_tags')->status('publish')->get();
		$params = ['body' => []];

		$i = 0;
		foreach($examples as $_example) {

			$params['body'][] = [
				'index' => [
					'_index' => $this->indices['example'],
					'_id' => $_example->ID
				]
			];

			$params['body'][] = [
				'id' => $_example->ID,
				'title' => $_example->post_title,
				'title_raw' => $_example->post_title,
                'tags' => $_example->acf ? collect(explode(',', $_example->acf->tags))->map(function($el){
                    return trim($el);
                }) : null,
			];

			// every 10 examples stop and send the bulk request
			if (($i > 0) && ($i % 10 == 0)) {

				$r_result = $this->client->bulk($params);

				if ($r_result["errors"]) {
					throw new Exception('Error importing examples in ES.');
				}

				// erase the previous bulk request
				$params = ['body' => []];
			}
			$i++;
		}

		// send the remaining batch if it exists
		if (!empty($params['body'])) {
			$result = $this->client->bulk($params);

			if ($result["errors"]) {
				throw new Exception('Error importing examples in ES.');
			}
		}

	}

    /**
     * @param int $example_id
     * @throws Exception
     */
    public function updateExample(int $example_id): void
    {
        $example = Post::type('example')->find($example_id);

        if (!$example) {
            throw new Exception('Example not found: ' . $example_id);
        }

        // delete from elastic
        $this->delete('example', $example->ID);

	    if($example->acf->articles) {
		    $this->deleteExampleArticles($example);
	    }

        // insert if status "publish"
        if ($example->post_status === 'publish') {

            $params = [
                'index' => $this->indices['example'],
                'id' => $example->ID,
                'body' => [
                    'id' => $example->ID,
                    'title' => $example->post_title,
                    'title_raw' => $example->post_title,
                    'tags' => $example->acf ? collect(explode(',', $example->acf->tags))->map(function ($el) {
                        return trim($el);
                    }) : null,
                ]
            ];

            $result = $this->client->index($params);

            if (isset($result["error"])) {
                throw new Exception('Error updating example in ES: '.$example_id);
            }

			if($example->acf->type === 'contract' && $example->acf->articles) {
				$this->importExampleArticles($example);
			}

        }
    }

    /**
	 * @throws Exception
	 */
	public function importArticles(): void
    {
		$posts = Post::type('example')->with('acf_articles')->status('publish')->get();

		// fetch all example articles
		$articles = [];

		foreach ($posts as $_post) {
		    if($_post->acf->articles){
                $_articles = $_post->acf->articles->toArray();

                if ($_articles) {
                    foreach ($_articles as $_article) {
                        if($_article['searchable']) {
                            $_article['post_id'] = $_post->ID;
                            $_article['post_title'] = $_post->post_title;
                            $articles[] = $_article;
                        }
                    }
                }
            }
		}

		$params = ['body' => []];

		$i = 0;
		foreach($articles as $_article) {

			$params['body'][] = [
				'index' => [
					'_index' => $this->indices['article'],
				]
			];

			$params['body'][] = [
				'post_id' => $_article['post_id'],
				'post_title' => $_article['post_title'],
				'title' => $_article['title'],
				'title_raw' => $_article['title'],
				'tags' => collect(explode(',', $_article['tags']))->map(function($el){
                    return trim($el);
                }),
				'description' => nl2br($_article['description']),
				'body' => StringHelper::wordpressContent($_article['body']),
			];

			// every 10 articles stop and send the bulk request
			if (($i > 0) && ($i % 10 == 0)) {

				$r_result = $this->client->bulk($params);

				if ($r_result["errors"]) {
					throw new Exception('Error importing articles in ES.');
				}

				// erase the previous bulk request
				$params = ['body' => []];
			}
			$i++;
		}

		// send the remaining batch if it exists
		if (!empty($params['body'])) {
			$result = $this->client->bulk($params);

			if ($result["errors"]) {
				throw new Exception('Error importing articles in ES.');
			}
		}
	}

    /**
     * @param $example
     * @return array|callable
     */
	public function deleteExampleArticles($example)
    {
        $params = [
            'index' => $this->indices['article'],
            'body'  => [
                'query' => [
                    'term' => [
                        'post_id' => $example->ID
                    ]
                ]
            ],
            'refresh' => true
        ];

        return $this->client->deleteByQuery($params);
    }

    /**
     * @param $example
     * @throws Exception
     */
    public function importExampleArticles($example)
    {
        $_articles = $example->acf->articles->toArray();

        $params = ['body' => []];

        $i = 0;
        foreach ($_articles as $_article) {
            if ($_article['searchable']) {
                $_article['post_id'] = $example->ID;
                $_article['post_title'] = $example->post_title;

                $params['body'][] = [
                    'index' => [
                        '_index' => $this->indices['article']
                    ]
                ];

                $params['body'][] = [
                    'post_id' => $_article['post_id'],
                    'post_title' => $_article['post_title'],
                    'title' => $_article['title'],
                    'title_raw' => $_article['title'],
                    'tags' => collect(explode(',', $_article['tags']))->map(function($el){
                        return trim($el);
                    }),
                    'description' => nl2br($_article['description']),
                    'body' => StringHelper::wordpressContent($_article['body']),
                ];

                // every 10 articles stop and send the bulk request
                if (($i > 0) && ($i % 10 == 0)) {
                    $r_result = $this->client->bulk($params);

                    if ($r_result["errors"]) {
                        throw new Exception('Error updating articles in ES.');
                    }

                    // erase the previous bulk request
                    $params = ['body' => []];
                }

                $i++;
            }
        }

        // send the remaining batch if it exists
        if (!empty($params['body'])) {
            $result = $this->client->bulk($params);

            if ($result["errors"]) {
                throw new Exception('Error updating articles in ES.');
            }
        }
    }


    /**
	 * @param string $index
	 * @param int $size
	 * @param int $from
	 * @param array $filters
	 *
	 * @return array
	 */
	public function get(string $index, int $size = 100, int $from = 0, array $filters = []): array {
		$body = [
			'from' => $from,
			'size' => $size,
		];

		// add filters
		if (!empty($filters)) {
			foreach($filters as $filterKey => $filter) {
				if ($filterKey === 'ids') {
					$body['query']['bool']['filter']['ids'] = [
						'values' => $filter
					];
				}

				if ($filterKey === 'post_ids') {
					$body['query']['bool']['filter']['terms'] = [
						'post_id' => $filter
					];
				}
			}
		}

		return $this->client->search([
			'index' => $this->indices[$index],
			'body' => $body
		]);
	}

    public static function prepareInput($string): string {
        return trim(Str::lower($string));
    }

	/**
	 * Constructs and returns body for ElasticSearch query
	 *
	 * @param string $query
	 * @param array $filters
	 *
	 * @return array
	 */
    private function constructBody(string $query, array $filters = []): array {
        $body = [
            'query' => [
                'bool' => [
                    'should' => [
                        ['match_phrase_prefix' => ['title_raw' => ['query' => $query, 'boost' => 4]]],
                        ['match' => ['title' => ['query' => $query, 'fuzziness' => 'AUTO', 'boost' => 3]]],
                        ['match' => ['tags' => ['query' => $query, 'fuzziness' => 'AUTO', 'boost' => 2]]],
                    ]
                ]
            ]
        ];

		// apply filters
	    if(!empty($filters)) {

			foreach($filters as $filter_key => $_filter) {
				if($filter_key === 'ids') {
					$body['query']['bool']['filter'] = [
						'ids' => [
							'values' => $_filter
						]
					];
				}

				if($filter_key === 'post_ids') {
					$body['query']['bool']['filter'] = [
						'terms' => [ // using "terms" query
							'post_id' => $filters['post_ids']
						]
					];
				}
			}

		    // ensure we don't always return matches because we're using filter query alongside should query
		    if(!empty($query)) {
			    $body['query']['bool']['minimum_should_match'] = 1;
		    }
	    }

		return $body;
    }

	/**
	 * @param string $index
	 * @param string $query
	 * @param $size (do not change to int - sometimes passing null!)
	 * @param int $from
	 * @param array $filters
	 *
	 * @return array
	 */
	public function search(string $index, string $query, $size = 10, int $from = 0, array $filters = []): array {
		$params = [
			'index' => $this->indices[$index],
			"from" => $from,
			"size" => $size,
		];

		$params['body'] = $this->constructBody($query, $filters);
		return $this->client->search($params);
	}

	/**
	 * @param $index
	 *
	 * @return int
	 */
    public static function getCount($index)
    {
        $es = new self();

        if ($es->existsIndex($es->indices[$index])) {
            return $es->client->count([
				'index' => $es->indices[$index]
            ])['count'];
        }

        return 0;
    }

}
