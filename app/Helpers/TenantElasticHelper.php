<?php

namespace App\Helpers;

use App\Models\DocumentTemplate;
use App\Models\Tenant;
use Elasticsearch\ClientBuilder;
use Exception;
use Illuminate\Support\Facades\App;

class TenantElasticHelper
{
    // API client
    private $client;
    private $indices;
    private $tenant;

    public function __construct(Tenant $tenant)
    {
        $this->tenant = $tenant;
        
        // initialize ElasticSearch API client
        $this->client = ClientBuilder::create()->setHosts([
            env('ELASTICSEARCH_HOST', 'localhost:9200')
        ])->build();

        // define tenant-specific indices (different names for different environments and tenants)
        $this->indices = [
            'template' => 'pravomat-templates-tenant-' . $tenant->id . '-' . App::environment(),
            'document' => 'pravomat-documents-tenant-' . $tenant->id . '-' . App::environment(),
            'example' => 'pravomat-examples-tenant-' . $tenant->id . '-' . App::environment(),
            'article' => 'pravomat-articles-tenant-' . $tenant->id . '-' . App::environment(),
        ];
    }

    public function isRunning(): bool {
        try {
            return $this->client->ping();
        } catch(Exception $e) {
            return false;
        }
    }

    /**
     * @throws Exception
     */
    public function restartIndices() {

        foreach($this->indices as $_index) {

            if($this->existsIndex($_index)) {
                $this->deleteIndex($_index);
            }

            $this->createIndex($_index);
        }
    }

    /**
     * @throws Exception
     */
    public function deleteIndex($index): array {
        return $this->client->indices()->delete(['index' => $index]);
    }

    private function fetchMappings($index): array {

        switch($index) {
            case $this->indices['template']:
                return [
                    "properties" => [
                        "id" => ["type" => "integer"],
                        "title" => [
                            "type" => "text",
                            "analyzer" => "croatian",
                            "fields" => [
                                "raw" => [
                                    "type" => "keyword"
                                ]
                            ]
                        ],
                        "title_raw" => ["type" => "keyword"],
                        "tags" => [
                            "type" => "text",
                            "analyzer" => "croatian"
                        ]
                    ]
                ];

            case $this->indices['document']:
            case $this->indices['example']:
                return [
                    "properties" => [
                        "id" => ["type" => "integer"],
                        "title" => [
                            "type" => "text",
                            "analyzer" => "croatian",
                            "fields" => [
                                "raw" => [
                                    "type" => "keyword"
                                ]
                            ]
                        ],
                        "title_raw" => ["type" => "keyword"],
                        "tags" => [
                            "type" => "text",
                            "analyzer" => "croatian"
                        ]
                    ]
                ];

            case $this->indices['article']:
                return [
                    "properties" => [
                        "id" => ["type" => "integer"],
                        "title" => [
                            "type" => "text",
                            "analyzer" => "croatian",
                            "fields" => [
                                "raw" => [
                                    "type" => "keyword"
                                ]
                            ]
                        ],
                        "title_raw" => ["type" => "keyword"],
                        "content" => [
                            "type" => "text",
                            "analyzer" => "croatian"
                        ]
                    ]
                ];

            default:
                throw new Exception('Unknown index: ' . $index);
        }
    }

    private function fetchSettings(): array {
        return [
            "analysis" => [
                "filter" => [
                    "hr_HR" => [
                        "type" => "hunspell",
                        "language" => "hr_HR"
                    ],
                ],
                "analyzer" => [
                    "croatian" => [
                        "tokenizer" => "standard",
                        "filter" => ["lowercase", "hr_HR"],
                        "char_filter" => [
                            "only_alphanumeric"
                        ]
                    ],
                ],
                "char_filter" => [
                    "only_alphanumeric" => [
                        "type" => "pattern_replace",
                        "pattern" => "[^a-zA-Z\d\s:]",
                        "replacement" => ""
                    ]
                ]
            ]
        ];
    }

    /**
     * @throws Exception
     */
    public function createIndex($index): array {
        return $this->client->indices()->create(
            [
                'index' => $index,
                'body' => [
                    'mappings' => $this->fetchMappings($index),
                    "settings" => $this->fetchSettings()
                ]
            ]
        );
    }

    public function existsIndex($index): bool {
        return $this->client->indices()->exists(['index' => $index]);
    }

    /**
     * @throws Exception
     */
    public function importTemplates()
    {
        $templates = DocumentTemplate::where('is_visible', 1)->get();
        $params = ['body' => []];

        $i = 0;
        foreach($templates as $_template) {

            $params['body'][] = [
                'index' => [
                    '_index' => $this->indices['template'],
                    '_id' => $_template->id
                ]
            ];

            $params['body'][] = [
                'id' => $_template->id,
                'title' => $_template->public_title,
                'title_raw' => $_template->public_title,
                'tags' => collect(explode(',', $_template->tags))->map(function($el){
                    return trim($el);
                }),
            ];

            // every 10 documents stop and send the bulk request
            if (($i > 0) && ($i % 10 == 0)) {

                $r_result = $this->client->bulk($params);

                if ($r_result["errors"]) {
                    throw new Exception('Error importing tenant templates in ES.');
                }

                // erase the previous bulk request
                $params = ['body' => []];
            }
            $i++;
        }

        // send the final bulk request
        if (!empty($params['body'])) {
            $r_result = $this->client->bulk($params);

            if ($r_result["errors"]) {
                throw new Exception('Error importing tenant templates in ES.');
            }
        }
    }

    /**
     * Sync templates for this tenant
     * @throws Exception
     */
    public function syncTemplates() {
        if (!$this->existsIndex($this->indices['template'])) {
            $this->createIndex($this->indices['template']);
        }
        
        $this->importTemplates();
    }

    /**
     * Update a specific template in the tenant's Elasticsearch index
     * @throws Exception
     */
    public function updateTemplate($template_id) {
        $template = DocumentTemplate::find($template_id);
        
        if (!$template) {
            throw new Exception('Template not found: ' . $template_id);
        }

        // delete from elastic
        $this->delete('template', $template->id);

        // insert if visible
        if ($template->is_visible) {
            $params = [
                'index' => $this->indices['template'],
                'id' => $template->id,
                'body' => [
                    'id' => $template->id,
                    'title' => $template->public_title,
                    'title_raw' => $template->public_title,
                    'tags' => collect(explode(',', $template->tags))->map(function($el){
                        return trim($el);
                    }),
                ]
            ];

            $result = $this->client->index($params);

            if (isset($result["error"])) {
                throw new Exception('Error updating tenant template in ES: '.$template_id);
            }
        }
    }

    /**
     * Delete a document from Elasticsearch
     * @throws Exception
     */
    public function delete($index, $id) {
        try {
            $this->client->delete([
                'index' => $this->indices[$index],
                'id' => $id
            ]);
        } catch (Exception $e) {
            // Document might not exist, which is fine
            if (strpos($e->getMessage(), 'not_found') === false) {
                throw $e;
            }
        }
    }

    /**
     * Search in tenant-specific index
     * @param string $index
     * @param string $query
     * @param $size
     * @param int $from
     * @param array $filters
     * @return array
     */
    public function search(string $index, string $query, $size = 10, int $from = 0, array $filters = []): array {
        $params = [
            'index' => $this->indices[$index],
            "from" => $from,
            "size" => $size,
        ];

        $params['body'] = $this->constructBody($query, $filters);
        return $this->client->search($params);
    }

    /**
     * Construct search body
     */
    private function constructBody($query, $filters = []): array {
        $body = [
            "query" => [
                "bool" => [
                    "must" => [
                        [
                            "multi_match" => [
                                "query" => $query,
                                "fields" => ["title^2", "tags"]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        if (!empty($filters)) {
            $body["query"]["bool"]["filter"] = $filters;
        }

        return $body;
    }

    /**
     * Get count of documents in tenant index
     */
    public function getCount($index): int {
        if ($this->existsIndex($this->indices[$index])) {
            return $this->client->count([
                'index' => $this->indices[$index]
            ])['count'];
        }

        return 0;
    }
}
