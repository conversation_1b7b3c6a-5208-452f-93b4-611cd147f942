<?php

namespace App\Observers;

use App\Models\DocumentTemplate;
use Stancl\Tenancy\Facades\Tenancy;

class TenantDocumentTemplateObserver
{
    /**
     * Handle the DocumentTemplate "created" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
    public function created(DocumentTemplate $documentTemplate)
    {
        if (tenancy()->initialized) {
            $tenant = Tenancy::tenant();
            \Artisan::call('tenant:es-sync', [
                'tenant' => $tenant->id
            ]);
        }
    }

    /**
     * Handle the DocumentTemplate "updated" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
    public function updated(DocumentTemplate $documentTemplate)
    {
        if (tenancy()->initialized) {
            $tenant = Tenancy::tenant();
            \Artisan::call('tenant:es-sync', [
                'tenant' => $tenant->id,
                'index' => 'template',
                'id' => $documentTemplate->id
            ]);

            \Cache::tags(['tenant-template-search-' . $tenant->id])->flush();
        }
    }

    /**
     * Handle the DocumentTemplate "saved" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
    public function saved(DocumentTemplate $documentTemplate)
    {
        if (tenancy()->initialized) {
            $tenant = Tenancy::tenant();
            \Artisan::call('tenant:es-sync', [
                'tenant' => $tenant->id
            ]);
        }
    }

    /**
     * Handle the DocumentTemplate "deleted" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
    public function deleted(DocumentTemplate $documentTemplate)
    {
        if (tenancy()->initialized) {
            $tenant = Tenancy::tenant();
            \Artisan::call('tenant:es-sync', [
                'tenant' => $tenant->id
            ]);
        }
    }

    /**
     * Handle the DocumentTemplate "restored" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
    public function restored(DocumentTemplate $documentTemplate)
    {
        if (tenancy()->initialized) {
            $tenant = Tenancy::tenant();
            \Artisan::call('tenant:es-sync', [
                'tenant' => $tenant->id
            ]);
        }
    }

    /**
     * Handle the DocumentTemplate "force deleted" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
    public function forceDeleted(DocumentTemplate $documentTemplate)
    {
        if (tenancy()->initialized) {
            $tenant = Tenancy::tenant();
            \Artisan::call('tenant:es-sync', [
                'tenant' => $tenant->id
            ]);
        }
    }
}
