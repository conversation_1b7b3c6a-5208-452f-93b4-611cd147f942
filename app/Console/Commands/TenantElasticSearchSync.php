<?php

namespace App\Console\Commands;

use App\Helpers\TenantElasticHelper;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Stancl\Tenancy\Facades\Tenancy;

class TenantElasticSearchSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenant:es-sync {tenant} {index?} {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync tenant document template models with Elastic Search';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle() {
        $tenant_id = $this->argument('tenant');
        $index = $this->argument('index');
        $id = $this->argument('id');

        // Find the tenant
        $tenant = Tenant::find($tenant_id);
        if (!$tenant) {
            $this->error("Tenant with ID '{$tenant_id}' not found.");
            return Command::FAILURE;
        }

        // Initialize tenancy
        Tenancy::initialize($tenant);

        try {
            // Check if both index and id arguments are provided
            if ($index !== null && $id !== null) {
                $this->syncPost($tenant, $index, $id);
            } else if ($index !== null && $id === null) {
                // If "index" is provided and "id" is not, return an error
                $this->error('The "id" argument must be provided if the "index" argument is present.');
                return Command::FAILURE;
            } else {
                $this->syncAll($tenant);
            }

            $this->info("Tenant Elasticsearch sync completed for tenant: {$tenant_id}");
        } catch (\Exception $e) {
            $this->error("Error syncing tenant Elasticsearch: " . $e->getMessage());
            return Command::FAILURE;
        } finally {
            // End tenancy
            Tenancy::end();
        }

        return Command::SUCCESS;
    }

    private function syncPost(Tenant $tenant, $index, $id): void {
        $es = new TenantElasticHelper($tenant);
        
        switch ($index) {
            case 'template':
                $es->updateTemplate($id);
                break;
            default:
                throw new \Exception("Unsupported index for tenant sync: {$index}");
        }
        
        $this->info("Synced {$index} with ID {$id} for tenant {$tenant->id}");
    }

    private function syncAll(Tenant $tenant): void {
        $es = new TenantElasticHelper($tenant);

        $this->info("Restarting tenant indices for tenant: {$tenant->id}");
        $es->restartIndices();

        $this->info("Importing tenant templates...");
        $es->importTemplates();

        $this->info("Tenant sync completed for: {$tenant->id}");
    }
}
