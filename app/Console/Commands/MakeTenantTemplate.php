<?php

/**
Creates a new document template for tenants
Prompts the user for various input fields such as title, public title, time to fill, slug, type, category, order index, sections, tags, etc.
After collecting the necessary information, it generates tenant migrations, runs tenant migrations, clears the cache, syncs tenant Elasticsearch, creates boilerplate files for the template and updates webpack.mix.js
 */

namespace App\Console\Commands;

use App\Helpers\TenantElasticHelper;
use App\Models\DocumentCategory;
use App\Models\DocumentCategoryTemplate;
use App\Models\DocumentTemplate;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Symfony\Component\Console\Helper\Table;
use Stancl\Tenancy\Facades\Tenancy;

class MakeTenantTemplate extends Command {

	protected $signature = 'make:tenant-template {tenant?}';
	protected $description = 'Creates a new document template for a specific tenant';

	private $tenant;
	private $title;
	private $public_title;
	private $time_to_fill;
	private $slug;
	private $precontract_for_id;
	private $type_id;
	private $type_name;
	private $category_id;
	private $category_title;
	private $order_index;
	private $sections = [];
	private $tags;

	public function handle() {

		// Only allow in local environment
		if ( ! app()->environment('local')) {
			$this->error('Cannot be run outside local environment.');
			return Command::FAILURE;
		}

		// Select tenant
		if (!$this->selectTenant()) {
			return Command::FAILURE;
		}

		// Initialize tenancy for the selected tenant
		Tenancy::initialize($this->tenant);

		// Check if Elasticsearch is running
		if(!(new TenantElasticHelper($this->tenant))->isRunning()) {
			$this->error('Elasticsearch server must be up and running.');
			return Command::FAILURE;
		}

		// Retrieve required information from the user
		$this->askTitle();
		$this->askPublicTitle();
		$this->askTimeToFill();
		$this->askSlug();
		$this->askTypeId();
		$this->askCategoryId();
		$this->askOrderIndex();
		$this->askSections();
		$this->askTags();
		$this->askPrecontractForId();

		// Display entered inputs and ask for confirmation
		$this->displayEnteredInputs();

		if ($this->confirm('Confirm the entered inputs?')) {

			$this->generateTenantMigrations();
			$this->runTenantMigrations();
			$this->clearCache();
			$this->syncTenantES();

			$this->createTenantBoilerplate();

			$this->info('Tenant template created successfully!');
			$this->info(PHP_EOL.'Please run "npm run dev" to compile new assets and ensure everything is working correctly.'.PHP_EOL);
		} else {
			$this->error('Operation cancelled.');
		}

		// End tenancy
		Tenancy::end();

		return Command::SUCCESS;
	}

	private function selectTenant(): bool {
		$tenant_id = $this->argument('tenant');

		if ($tenant_id) {
			$this->tenant = Tenant::find($tenant_id);
			if (!$this->tenant) {
				$this->error("Tenant with ID '{$tenant_id}' not found.");
				return false;
			}
		} else {
			// Show available tenants
			$tenants = Tenant::all();

			if ($tenants->isEmpty()) {
				$this->error('No tenants found.');
				return false;
			}

			$table = new Table($this->output);
			$table->setHeaders(['ID', 'Domains']);

			foreach ($tenants as $tenant) {
				$domains = $tenant->domains->pluck('domain')->implode(', ');
				$table->addRow([$tenant->id, $domains ?: 'No domains']);
			}

			$table->render();

			$tenant_id = $this->ask('Enter the tenant ID');
			$this->tenant = Tenant::find($tenant_id);

			if (!$this->tenant) {
				$this->error("Tenant with ID '{$tenant_id}' not found.");
				return false;
			}
		}

		$this->info("Selected tenant: {$this->tenant->id}");
		return true;
	}

	private function generateTenantMigrations() {

		$existing_files = collect(File::files(database_path('migrations/tenant')))
			->map(fn($file) => $file->getFilename());

		Artisan::call('make:migration', [
			'name' => 'insert_template_' . Str::snake($this->title),
			'--path' => 'database/migrations/tenant'
		]);

		$new_files = collect(File::files(database_path('migrations/tenant')))
			->reject(fn($file) => $existing_files->contains($file->getFilename()))
			->values();

		if ($new_files->isEmpty()) {
			throw new \Exception('Failed to detect the newly created tenant migration file.');
		}

		$migration_path = $new_files->first()->getPathname();

		if (!is_file($migration_path)) {
			throw new \Exception("Expected a file, but got an invalid path: $migration_path");
		}

		$up_method_body = "
        DB::insert(\"INSERT INTO `document_templates` (
            `title`, `public_title`, `tags`, `time_to_fill`, `slug`,
            `precontract_for_id`, `type_id`, `is_visible`, `created_at`, `updated_at`
        ) VALUES (
            '{$this->title}', '{$this->public_title}', " . ($this->tags !== null ? "'{$this->tags}'" : "NULL") . ",
            '{$this->time_to_fill}', '{$this->slug}', " . ($this->precontract_for_id !== null ? "'{$this->precontract_for_id}'" : "NULL") . ",
            '{$this->type_id}', 1, NOW(), NOW())\");

        \$result = DB::select('SELECT MAX(id) as id FROM document_templates');
        \$template_id = intval(\$result[0]->id);

        DB::insert(\"INSERT INTO `document_categories_templates` (
            `category_id`, `template_id`, `order_index`, `created_at`, `updated_at`
        ) VALUES ('{$this->category_id}', ?, '{$this->order_index}', NOW(), NOW())\", [\$template_id]);
    ";

		foreach ($this->sections as $section) {
			$up_method_body .= "
        DB::insert(\"INSERT INTO `document_template_sections` (
            `template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`
        ) VALUES (?, '{$section['title']}', '{$section['view']}', '{$section['order_index']}', '{$section['nav_title']}', NOW(), NOW())\", [\$template_id]);
        ";
		}

		$down_method_body = "
        \$template_id = DB::table('document_templates')->where('title', '{$this->title}')->value('id');
        DB::table('document_templates')->where('id', \$template_id)->delete();
        DB::table('document_categories_templates')->where('template_id', \$template_id)->delete();
        DB::table('document_template_sections')->where('template_id', \$template_id)->delete();
    ";

		$migration_content = file_get_contents($migration_path);

		// Update the anonymous up() method
		$migration_content = preg_replace_callback(
			'/public function up\(\): void\s*\{[^}]*\}/s',
			fn() => "public function up(): void\n    {\n" . $up_method_body . "\n    }",
			$migration_content
		);

		// Update the anonymous down() method
		$migration_content = preg_replace_callback(
			'/public function down\(\): void\s*\{[^}]*\}/s',
			fn() => "public function down(): void\n    {\n" . $down_method_body . "\n    }",
			$migration_content
		);

		file_put_contents($migration_path, $migration_content);

		$this->info('Tenant migration file generated and updated successfully.');
	}

	private function runTenantMigrations() {

		$this->info('Running tenant migrations...');
		Artisan::call('tenants:migrate', [
			'--tenants' => [$this->tenant->id]
		]);
		$this->info('Tenant migrations successful.');
	}

	private function clearCache() {

		Artisan::call('cache:clear');
		Artisan::call('modelCache:clear');
	}

	private function syncTenantES() {

		$tenantElastic = new TenantElasticHelper($this->tenant);
		$tenantElastic->syncTemplates();
	}

	private function createTenantBoilerplate() {

		$this->info("Creating tenant boilerplate...");

		// Create tenant view files
		$directory_view_path = resource_path("views/tenant/documents/{$this->title}");
		File::makeDirectory($directory_view_path, 0755, true);
		File::makeDirectory("$directory_view_path/template", 0755, true);

		File::put("$directory_view_path/template/data.blade.php", <<<'EOD'
@extends('layouts.tenant.document.template')
@section('content')
    @php
	    // define variables for rendering view
	    $data = [];

    @endphp

    @include($builder->render_view, ['builder' => $builder, 'data' => $data])

@endsection
EOD);

		File::put("$directory_view_path/template/render.blade.php", "");

		foreach ($this->sections as $_section) {
			File::put("$directory_view_path/{$_section['view']}.blade.php", <<<EOD
@extends('layouts.tenant.document.master')
@section('content')
    {{ Form::model(\$model, ['url' => \$route, 'autocomplete' => 'off' ]) }}
    <div class='row'>
        <div class='col'>
            <div class='card mb-4'>
                <div class='card-header'>
                    {$_section['title']}
                </div>
                <div class='card-body'>
                    <div class='row'>
                        <div class='form-group col-lg-12'>

						</div>
					</div>
				</div>
            </div>
        </div>
    </div>
    {{ Form::close() }}
@endsection
EOD);
		}

		// Create tenant javascript files
		$directory_js_path = resource_path("assets/js/tenant/documents/{$this->title}");
		File::makeDirectory($directory_js_path, 0755, true);

		foreach ($this->sections as $_section) {
			File::put("$directory_js_path/{$_section['view']}.js", <<<EOD
$(document).ready(function () {

});
EOD);
		}

		// Update webpack for tenant assets
		$this->updateWebpackForTenant();
	}

	private function updateWebpackForTenant() {
		$webpack_mix_path = base_path('webpack.mix.js');
		$webpack_mix_contents = File::get($webpack_mix_path);

		$new_lines = "";
		foreach ($this->sections as $_i => $_section) {
			$new_lines .= ($_i == 0)
				? ".js('resources/assets/js/tenant/documents/{$this->title}/{$_section['view']}.js', 'public/js/tenant/documents/{$this->title}/')"
				: PHP_EOL . "\t.js('resources/assets/js/tenant/documents/{$this->title}/{$_section['view']}.js', 'public/js/tenant/documents/{$this->title}/')";
		}

		$modified_contents = str_replace(
			'//{addTenantEntryPlaceholder}',
			"$new_lines\n\t//{addTenantEntryPlaceholder}",
			$webpack_mix_contents
		);

		File::put($webpack_mix_path, $modified_contents);
	}

	private function displayEnteredInputs() {

		$this->info("\nEntered Inputs:");
		$this->table(
			['Field', 'Value'],
			[
				['Tenant ID', $this->tenant->id],
				['Title', $this->title],
				['Public Title', $this->public_title],
				['Time to Fill', $this->time_to_fill],
				['Slug', $this->slug],
				['Type', $this->type_name],
				['Category', $this->category_title],
				['Order Index', $this->order_index],
				['Sections', $this->formatSections()],
				['Tags', $this->tags ?? 'Not provided'],
				['Pre-contract For ID', $this->precontract_for_id ?? 'Not provided'],
			]
		);
	}

	private function formatSections() {

		$formatted_sections = '';

		foreach ($this->sections as $section) {
			$formatted_sections .= "#{$section['order_index']}\n";
			$formatted_sections .= "    Title: {$section['title']}\n";
			$formatted_sections .= "    Nav Title: {$section['nav_title']}\n";
			$formatted_sections .= "    View: {$section['view']}\n\n";
		}

		return $formatted_sections;
	}

	private function askUntilValid($question, $error_message = "Field is required") {

		$value = $this->ask($question);

		while (empty(trim($value))) {
			$this->error($error_message);
			$value = $this->ask($question);
		}

		return $value;
	}

	private function askTitle() {

		$this->title = $this->askUntilValid('Enter the title (english, UpperCamelCase)');
	}

	private function askPublicTitle() {

		$this->public_title = $this->askUntilValid('Enter the public title');
	}

	private function askTimeToFill() {

		$this->time_to_fill = $this->askUntilValid('Enter the time to fill (in minutes)');
	}

	private function askSlug() {

		$this->slug = $this->askUntilValid('Enter the url slug (hyphenated-slug)');
	}

	private function askTypeId() {

		// Prepare the table
		$table = new Table($this->output);
		$table->setHeaders(['ID', 'Title']);

		// Add rows to the table
		foreach (DocumentTemplate::$types as $_type_name => $_type_id) {
			$table->addRow([$_type_id, $_type_name]);
		}

		// Render the table
		$table->render();

		$type_id = $this->askUntilValid('Enter the type ID');

		while ( ! in_array($type_id, array_values(DocumentTemplate::$types))) {
			$this->error('Invalid type ID.');
			$type_id = $this->askUntilValid('Enter the type ID');
		}

		$this->type_id   = $type_id;
		$this->type_name = array_flip(DocumentTemplate::$types)[$type_id];
	}

	private function askCategoryId() {

		// Fetch the data from the DocumentCategory model (in tenant context)
		$categories = DocumentCategory::select('id', 'title')->orderBy('id')->get();

		// Prepare the table
		$table = new Table($this->output);
		$table->setHeaders(['ID', 'Title']);

		// Add rows to the table
		foreach ($categories as $_category) {
			$table->addRow([$_category->id, $_category->title]);
		}

		// Render the table
		$table->render();

		$category_id = $this->askUntilValid('Enter the category ID');

		while ( ! in_array($category_id, $categories->pluck('id')->toArray())) {
			$this->error('Invalid category ID.');
			$category_id = $this->askUntilValid('Enter the category ID');
		}

		$this->category_id    = $category_id;
		$this->category_title = $categories->firstWhere('id', $category_id)->title;
	}

	private function askOrderIndex() {

		// Fetch the data from the DocumentCategoryTemplate model (in tenant context)
		$order_indexes = DocumentCategoryTemplate::where('category_id', $this->category_id)->pluck('order_index')->toArray();

		// Prepare the table
		$table = new Table($this->output);
		$table->setHeaders(['#']);

		// Add rows to the table
		foreach ($order_indexes as $_order_index) {
			$table->addRow([$_order_index]);
		}

		// Render the table
		$table->render();

		$this->order_index = $this->askUntilValid('Enter order index:');
	}

	private function askSections() {

		$this->info('Please define template sections');
		$sections = [];
		$i        = 1;
		while (true) {
			$section                = [];
			$section['order_index'] = $i;
			$section['title']       = $this->askUntilValid('Enter the section title (full)');
			$section['nav_title']   = $this->askUntilValid('Enter the section navigation title');
			$section['view']        = mb_strtolower($this->askUntilValid('Enter the section view (snake_case)'), 'UTF-8');

			$sections[] = $section;

			if ( ! $this->confirm('Add another section?')) {
				break;
			}

			$i ++;
		}

		$this->sections = $sections;
	}

	private function askTags() {

		$this->tags = $this->ask('Enter the comma-separated tags (optional)');
	}

	private function askPrecontractForId() {

		// Fetch the data from the DocumentTemplate model (in tenant context)
		$templates = DocumentTemplate::whereNull('precontract_for_id')->select('id', 'title')->orderBy('id')->get();

		// Prepare the table
		$table = new Table($this->output);
		$table->setHeaders(['ID', 'Title']);

		// Add rows to the table
		foreach ($templates as $_template) {
			$table->addRow([$_template->id, $_template->title]);
		}

		// Render the table
		$table->render();
		$this->precontract_for_id = $this->ask('For a pre-contract template, enter document template ID (optional)');
	}
}
