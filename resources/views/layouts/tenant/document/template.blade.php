<!DOCTYPE html>
<html lang="hr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name') }} - @yield('title', 'Template')</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="{{ mix('css/tenant.css') }}" rel="stylesheet">

    <!-- Template specific styles -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #ffffff;
            color: #333;
            line-height: 1.6;
        }
        
        .template-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .template-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #4A6FDC;
        }
        
        .template-content {
            margin-bottom: 30px;
        }
        
        .template-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            font-size: 0.9em;
            color: #6c757d;
        }
        
        @media print {
            body {
                background-color: white;
            }
            
            .template-container {
                max-width: none;
                margin: 0;
                padding: 0;
            }
            
            .no-print {
                display: none !important;
            }
        }
    </style>

    @stack('styles')
</head>
<body>
    <div class="template-container">
        <div class="template-header">
            <h1>{{ config('app.name') }}</h1>
            <p class="lead">Tenant Document Template</p>
        </div>

        <div class="template-content">
            @yield('content')
        </div>

        <div class="template-footer">
            <p>Generated on {{ date('d.m.Y H:i') }} | {{ config('app.name') }} Tenant Portal</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ mix('js/app.js') }}"></script>

    @stack('scripts')
</body>
</html>
