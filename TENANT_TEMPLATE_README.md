# Tenant Template System

This document describes the tenant-specific template system that allows creating document templates for individual tenants with separate databases, folder structures, and Elasticsearch indices.

## Overview

The tenant template system extends the existing `MakeTemplate` command to support multi-tenant architecture where:

- Each tenant has its own database
- Templates are stored in tenant-specific databases
- Elasticsearch uses tenant-specific indices
- Views and assets are organized in tenant-specific folders
- Webpack compilation supports tenant-specific assets

## Commands

### Create Tenant Template

```bash
php artisan make:tenant-template [tenant_id]
```

**Parameters:**
- `tenant_id` (optional): The ID of the tenant. If not provided, a list of available tenants will be displayed.

**Example:**
```bash
# Show available tenants and prompt for selection
php artisan make:tenant-template

# Create template for specific tenant
php artisan make:tenant-template abc123-def456-ghi789
```

### Sync Tenant Elasticsearch

```bash
php artisan tenant:es-sync {tenant} [index] [id]
```

**Parameters:**
- `tenant` (required): The tenant ID
- `index` (optional): Specific index to sync (e.g., 'template')
- `id` (optional): Specific document ID to sync

**Examples:**
```bash
# Sync all indices for a tenant
php artisan tenant:es-sync abc123-def456-ghi789

# Sync specific template for a tenant
php artisan tenant:es-sync abc123-def456-ghi789 template 123
```

## File Structure

### Tenant Views
Templates create views in the tenant-specific directory:
```
resources/views/tenant/documents/{TemplateName}/
├── template/
│   ├── data.blade.php
│   └── render.blade.php
├── section1.blade.php
├── section2.blade.php
└── ...
```

### Tenant Assets
JavaScript files are created in:
```
resources/assets/js/tenant/documents/{TemplateName}/
├── section1.js
├── section2.js
└── ...
```

Compiled assets go to:
```
public/js/tenant/documents/{TemplateName}/
├── section1.js
├── section2.js
└── ...
```

### Tenant Migrations
Migrations are created in:
```
database/migrations/tenant/
└── YYYY_MM_DD_HHMMSS_insert_template_{template_name}.php
```

## Elasticsearch Integration

### Index Naming Convention
Tenant-specific indices follow this pattern:
```
pravomat-{type}-tenant-{tenant_id}-{environment}
```

**Examples:**
- `pravomat-templates-tenant-abc123-local`
- `pravomat-documents-tenant-abc123-production`

### TenantElasticHelper
The `TenantElasticHelper` class provides tenant-specific Elasticsearch operations:

```php
use App\Helpers\TenantElasticHelper;
use App\Models\Tenant;

$tenant = Tenant::find('abc123-def456-ghi789');
$elasticHelper = new TenantElasticHelper($tenant);

// Check if Elasticsearch is running
$elasticHelper->isRunning();

// Sync templates for this tenant
$elasticHelper->syncTemplates();

// Search in tenant-specific index
$results = $elasticHelper->search('template', 'search query');
```

## Database Structure

### Tenant Database Tables
Each tenant database contains the same structure as the main application:
- `document_templates`
- `document_categories`
- `document_categories_templates`
- `document_template_sections`
- And other related tables...

### Migration Process
1. Migrations are generated in `database/migrations/tenant/`
2. Migrations are run using `tenants:migrate` command
3. Only the specified tenant's database is affected

## Webpack Integration

### Configuration
The webpack configuration includes a placeholder for tenant assets:
```javascript
// Tenant document JS files
//{addTenantEntryPlaceholder}
```

### Asset Compilation
When creating a tenant template, webpack entries are automatically added:
```javascript
.js('resources/assets/js/tenant/documents/TemplateName/section.js', 'public/js/tenant/documents/TemplateName/')
```

### Building Assets
After creating tenant templates, compile assets:
```bash
npm run dev
# or
npm run production
```

## Layout Files

### Tenant Document Layouts
Two layout files are provided for tenant documents:

1. **Master Layout** (`layouts/tenant/document/master.blade.php`)
   - Full page layout with navigation
   - Used for document editing forms
   - Includes tenant branding

2. **Template Layout** (`layouts/tenant/document/template.blade.php`)
   - Minimal layout for document rendering
   - Used for document templates and previews
   - Print-friendly styling

## Observer Integration

### TenantDocumentTemplateObserver
Automatically handles Elasticsearch syncing when tenant templates are modified:

- **Created**: Syncs all tenant indices
- **Updated**: Syncs specific template
- **Deleted**: Syncs all tenant indices
- **Restored**: Syncs all tenant indices

The observer only operates when tenancy is initialized, ensuring proper tenant context.

## Usage Workflow

1. **Create Tenant Template**
   ```bash
   php artisan make:tenant-template
   ```

2. **Follow Interactive Prompts**
   - Select tenant
   - Enter template details (title, slug, sections, etc.)
   - Confirm inputs

3. **System Actions**
   - Generates tenant migration
   - Runs tenant migration
   - Creates tenant-specific views and assets
   - Updates webpack configuration
   - Syncs tenant Elasticsearch

4. **Compile Assets**
   ```bash
   npm run dev
   ```

5. **Verify Template**
   - Check tenant database for new template
   - Verify Elasticsearch index contains template
   - Test template views and functionality

## Troubleshooting

### Common Issues

1. **Elasticsearch Not Running**
   ```
   Error: Elasticsearch server must be up and running.
   ```
   Solution: Start Elasticsearch service

2. **Tenant Not Found**
   ```
   Error: Tenant with ID 'xyz' not found.
   ```
   Solution: Verify tenant ID exists in tenants table

3. **Migration Fails**
   ```
   Error: Failed to detect the newly created tenant migration file.
   ```
   Solution: Check file permissions on database/migrations/tenant directory

4. **Webpack Compilation Issues**
   ```
   Error: Module not found
   ```
   Solution: Ensure tenant asset files exist and webpack placeholder is present

### Debugging

1. **Check Tenant Context**
   ```php
   use Stancl\Tenancy\Facades\Tenancy;
   
   if (tenancy()->initialized) {
       $tenant = Tenancy::tenant();
       echo "Current tenant: " . $tenant->id;
   }
   ```

2. **Verify Elasticsearch Index**
   ```bash
   curl -X GET "localhost:9200/pravomat-templates-tenant-{tenant_id}-{env}/_search"
   ```

3. **Check Tenant Database**
   ```sql
   SELECT * FROM document_templates WHERE title = 'YourTemplateName';
   ```

## Security Considerations

- Tenant data is completely isolated in separate databases
- Elasticsearch indices are tenant-specific
- Views and assets are organized by tenant
- Observer checks ensure proper tenant context
- Commands require explicit tenant specification

## Performance Notes

- Each tenant has its own Elasticsearch indices for better performance
- Tenant-specific asset compilation allows for optimized loading
- Database isolation prevents cross-tenant data leakage
- Observer operations are optimized for tenant context
