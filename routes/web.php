<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Response;

// Controller Imports
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\DocumentAIController;
use App\Http\Controllers\ApiController;
use App\Http\Controllers\Auth\SocialAuthController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\DebugController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\EditorController;
use App\Http\Controllers\EmailChangeController;
use App\Http\Controllers\EmailController;
use App\Http\Controllers\EmailPreferencesController;
use App\Http\Controllers\FeedbackController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PersonalDataController;
use App\Http\Controllers\RedirectController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\SectionController;
use App\Http\Controllers\SignatureController;
use App\Http\Controllers\TranslationController;
use App\Http\Controllers\TranslationDocumentController;
use App\Http\Controllers\TranslationInvoiceController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\WordpressController;

// only allow in local environment
Route::group(['middleware' => ['local']], function () {
	Route::any('/debug', [DebugController::class, 'index']);
});

// impersonate user, generate signed URL via tinker - URL::temporarySignedRoute('impersonate', now()->addMinutes(30), ['id' => $id])
Route::get('/impersonate', function() {
	Auth::logout();
	Auth::loginUsingId(request()->get('id'));
	return redirect(route('home'));
})->name('impersonate')->middleware('signed');

// invalidate browser "back" cache
Route::group(['middleware' => ['cache.headers:no_cache;no_store;must_revalidate;max_age=0']], function () {

	// home route
	Route::get('/', function () {

		// check if WordPress draft preview
		if (request('preview')) {

			$controller = app()->make(WordpressController::class);

			// post
			if (request('p')) {
				return $controller->callAction('postDraft', [
					'id' => request('p')
				]);
				// page
			} elseif (request('page_id')) {
				return $controller->callAction('pageDraft', [
					'id' => request('page_id')
				]);
			}
		}

		$controller = app()->make(HomeController::class);

		return $controller->callAction('index', []);

	})->name('home');

	Route::get('/redirect/to', [RedirectController::class, 'to'])->name('redirect.to');

	Route::any('/document/show', [DocumentController::class, 'show'])->name('document.show');
	Route::any('/document/create', [DocumentController::class, 'create'])->name('document.create');
	Route::any('/document/create/{post}', [DocumentController::class, 'createThroughPost'])->name('document.createThroughPost');

	// signed routes
	Route::group(['middleware' => ['signed']], function () {

		// documents
		Route::get('/dokument/{document}/stream', [DocumentController::class, 'stream'])->name('document.stream');
		Route::post('/document/preview/html', [DocumentController::class, 'previewHtml'])->name('document.preview.html');
		Route::get('/dokument/{document}/pregled', [DocumentController::class, 'previewPdf'])->name('document.preview.image');
		Route::get('/dokument/{document}/preuzmi', [DocumentController::class, 'download'])->name('document.download');

		// drafts
		Route::get('/uredivac/{draft}/preuzmi', [EditorController::class, 'download'])->name('editor.draft.download');

		// examples
		Route::get('/primjeri/{example}/unprotected', [WordpressController::class, 'exampleUnprotected'])->name('wordpress.example.unprotected');

		// translations
		Route::get('/prijevod/{translation}/preuzmi', [TranslationDocumentController::class, 'downloadOutput'])->name('translation.download');

		// signatures
		Route::get('/stranka/{party}/potpis', [SignatureController::class, 'show'])->name('signature.show');
		Route::get('/stranka/{party}/potpis/image', [SignatureController::class, 'image'])->name('signature.image');
		Route::post('/party/{party}/signature/save', [SignatureController::class, 'save'])->name('signature.save');

		// email subscription
		Route::get('/postavke/obavijesti', [EmailPreferencesController::class, 'show'])->name('email.preferences.show');
		Route::post('/email/preferences/store', [EmailPreferencesController::class, 'store'])->name('email.preferences.store');

		// email address change
		Route::get('/email/update', [EmailChangeController::class, 'update'])->name('email.update');

		// contact files
		Route::get('/contact/{id}/file/{filename}', [ContactController::class, 'getFile'])->name('contact.file');

		// order confirmation (can be accessed publicly via email notification)
		Route::get('/prijevod/{translation}/potvrda', [TranslationController::class, 'downloadOrderConfirmation'])->name('translation.order.confirmation.download');

		// translation shipment tracking
		Route::get('/prijevod/{translation}/posiljka', [TranslationController::class, 'trackShipment'])->name('translation.shipment.track');
	});

	// document routes - restrict to document owners
	Route::group(['middleware' => ['auth', 'verified', 'can:update,document']], function () {
		Route::get('/dokument/{document}/izbrisi', [DocumentController::class, 'delete'])->name('document.delete');
		Route::get('/dokument/{document}/potpisi', [SignatureController::class, 'index'])->name('signature.index');
		Route::get('/dokument/{document}/posalji', [EmailController::class, 'document'])->name('email.document');
		Route::get('/dokument/{document}/dupliciraj', [DocumentController::class, 'duplicate'])->name('document.duplicate');
		Route::get('/dokument/{document}/izvezi', [DocumentController::class, 'exportToEditor'])->name('document.exportToEditor');
		Route::get('/dokument/{document}/prevedi', [DocumentController::class, 'translate'])->name('document.translate');
		Route::get('/dokument/{document}/fork', [DocumentController::class, 'fork'])->name('document.fork');
		Route::post('/dokument/{document}/send', [EmailController::class, 'sendDocument'])->name('email.sendDocument');
		Route::get('/dokument/{document}/stranka/{party}/zatrazi-potpis', [SignatureController::class, 'request'])->name('signature.request');
		Route::post('/dokument/{document}/party/{party}/signature/request', [SignatureController::class, 'requestSend'])->name('signature.request.send');
		Route::post('/dokument/{document}/signatureRequest/{request}/revoke', [SignatureController::class, 'requestRevoke'])->name('signature.request.revoke');
		Route::any('/document/{document}/comment', [DocumentController::class, 'comment'])->name('document.comment');
	});

	// document section (wizard) routes
	Route::group(['middleware' => ['can:update,document']], function () {
		Route::get('/dokument/{document}/{section}', [SectionController::class, 'show'])->name('section.show');
		Route::post('/dokument/{document}/{section}', [SectionController::class, 'store'])->name('section.store');
	});

	// logged-in user routes
	Route::group(['middleware' => ['auth', 'verified']], function () {
		Route::get('/moji-dokumenti', [DocumentController::class, 'index'])->name('documents');
		Route::get('/profil', [UserController::class, 'dashboard'])->name('user.dashboard');
		Route::get('/user/documents', [DocumentController::class, 'dataTables'])->name('user.documents');
		Route::get('/user/drafts', [EditorController::class, 'draftsDataTables'])->name('user.drafts');
		Route::get('/user/translations', [TranslationController::class, 'dataTables'])->name('user.translations');

		// save tutorial shown flags
		Route::post('/user/documents/tutorial', [UserController::class, 'setDocumentTutorialShown'])->name('documents.tutorial');
		Route::post('/user/editor/draft/tutorial', [UserController::class, 'setDraftTutorialShown'])->name('editor.draft.tutorial');
		Route::post('/user/editor/tutorial', [UserController::class, 'setEditorTutorialShown'])->name('editor.tutorial');
		Route::post('/user/translation/tutorial', [UserController::class, 'setTranslationTutorialShown'])->name('translation.tutorial');

		Route::get('uredi/podatke', [PersonalDataController::class, 'show'])->name('account.personal.data');
		Route::post('uredi/podatke', [PersonalDataController::class, 'update'])->name('account.personal.data.update');
		Route::post('account/delete', [UserController::class, 'deleteAccount'])->name('account.delete');

		// editor routes
		Route::group(['middleware' => ['can:update,draft']], function () {
			Route::get('/uredivac/{draft}', [EditorController::class, 'edit'])->name('editor.draft.edit');
			Route::post('/editor/{draft}/save', [EditorController::class, 'save'])->name('editor.draft.save');
			Route::get('/uredivac/{draft}/izbrisi', [EditorController::class, 'delete'])->name('editor.draft.delete');
			Route::any('/editor/{draft}/comment', [EditorController::class, 'comment'])->name('editor.draft.comment');
			Route::any('/editor/{draft}/title', [EditorController::class, 'title'])->name('editor.draft.title');
			Route::get('/editor/{draft}/stream', [EditorController::class, 'stream'])->name('editor.draft.stream');
			Route::get('/uredivac/{draft}/dupliciraj', [EditorController::class, 'duplicate'])->name('editor.draft.duplicate');
			Route::get('/uredivac/dokument/{draft}/posalji', [EmailController::class, 'draft'])->name('editor.draft.email');
			Route::get('/uredivac/dokument/{draft}/prevedi', [EditorController::class, 'translate'])->name('editor.draft.translate');
			Route::post('/editor/{draft}/send', [EmailController::class, 'sendDraft'])->name('editor.draft.send');
		});

		Route::get('/editor/{post}/articles/search', [EditorController::class, 'articlesDataTables'])->name('editor.articles');
		Route::post('/editor/spellcheck', [EditorController::class, 'spellCheck'])->name('editor.spellcheck')->middleware('throttle:30,1');

		// translation routes
		Route::group(['middleware' => ['can:update,translation']], function () {
			Route::get('/prijevod/{translation}/dokumenti/preuzmi', [TranslationDocumentController::class, 'downloadInput'])->name('translation.download.input');
			Route::get('/prijevod/{translation}/posalji', [EmailController::class, 'translation'])->name('email.translation');
			Route::post('/prijevod/{translation}/send', [EmailController::class, 'sendTranslation'])->name('email.sendTranslation');
			Route::get('/prijevod/{translation}', [TranslationController::class, 'show'])->name('translation.show');
			Route::get('/prijevod/{translation}/uredi', [TranslationController::class, 'edit'])->name('translation.edit');
			Route::get('/translation/{translation}/reinvoice', [TranslationController::class, 'requestReinvoice'])->name('translation.request.reinvoice');
			Route::post('/translation/{translation}/update', [TranslationController::class, 'update'])->name('translation.update');
			Route::get('/prijevod/naruci/{translation}', [TranslationController::class, 'checkout'])->name('translation.checkout');
			Route::get('/prijevod/{translation}/otkazi', [TranslationController::class, 'cancel'])->name('translation.cancel');
			Route::get('/prijevod/{translation}/racun', [TranslationController::class, 'downloadReceipt'])->name('translation.receipt.download');
			Route::get('/prijevod/{translation}/storno/racun', [TranslationController::class, 'downloadStornoReceipt'])->name('translation.storno.receipt.download');
		});

		// translation document routes
		Route::group(['middleware' => ['can:update,document']], function () {
			Route::get('/prijevod/dokument/{document}/preuzmi', [TranslationDocumentController::class, 'download'])->name('translation.download.document');
			Route::get('/prijevod/dokument/{document}/preuzmi/prijevod', [TranslationDocumentController::class, 'downloadDocumentOutput'])->name('translation.download.document.output');
		});

		// translation invoice routes
		Route::group(['middleware' => ['can:update,invoice']], function () {
			Route::post('/translation/invoice/{invoice}/pay', [TranslationInvoiceController::class, 'pay'])->name('translation.invoice.pay');
			Route::get('/prijevod/ponuda/{invoice}/preuzmi', [TranslationInvoiceController::class, 'download'])->name('translation.invoice.download');
		});

		Route::post('/translation/process', [TranslationController::class, 'process'])->name('translation.process');

		// admin routes
		Route::group(['middleware' => ['admin']], function () {
			// Admin Dashboard
			Route::get('/admin', [AdminController::class, 'dashboard'])->name('admin.dashboard');

			if (app()->environment(['local', 'staging'])) {
				Route::get('/admin/document-ai', [DocumentAIController::class, 'index'])->name('admin.document-ai.index');
				Route::post('/admin/document-ai/process', [DocumentAIController::class, 'process'])->name('admin.document-ai.process');
				Route::get('/admin/document-ai/success', [DocumentAIController::class, 'success'])->name('admin.document-ai.success');
				Route::post('/admin/document-ai/restore-backups', [DocumentAIController::class, 'restoreBackups'])->name('admin.document-ai.restore-backups');
				Route::post('/admin/document-ai/check-backups', [DocumentAIController::class, 'checkBackups'])->name('admin.document-ai.check-backups');
			}

			Route::get('/uredivac/{draft}/dupliciraj/admin', [EditorController::class, 'duplicateAdmin'])->name('editor.draft.duplicate.admin');
			Route::get('/dokument/{document}/dupliciraj/admin', [DocumentController::class, 'duplicateAdmin'])->name('document.duplicate.admin');
			Route::get('/uredivac/{draft}/preuzmi/admin', [EditorController::class, 'downloadAdmin'])->name('editor.draft.download.admin');
			Route::get('/prijevod/{translation}/preuzmi/dokumenti/admin', [TranslationDocumentController::class, 'downloadInputAdmin'])->name('translation.download.input.admin');
			Route::post('/translation/{translation}/upload', [TranslationDocumentController::class, 'uploadOutput'])->name('translation.upload');
			Route::post('/translation/{translation}/upload/edit', [TranslationDocumentController::class, 'editOutput'])->name('translation.upload.edit');
			Route::get('/prijevod/{translation}/admin', [TranslationController::class, 'show'])->name('translation.show.admin');
			Route::get('/prijevod/{translation}/autoriziraj', [TranslationController::class, 'authorizePayment'])->name('translation.payment.authorize');
			Route::post('/translation/{translation}/reject', [TranslationController::class, 'reject'])->name('translation.reject');
			Route::post('/prijevod/{translation}/void', [TranslationController::class, 'void'])->name('translation.void');
			Route::post('/translation/{translation}/ship', [TranslationController::class, 'ship'])->name('translation.ship');
			Route::post('/translation/{translation}/shipment/edit', [TranslationController::class, 'editShipment'])->name('translation.shipment.edit');
			Route::get('/prijevod/narudzba/{translation}/dostavljeno', [TranslationController::class, 'deliver'])->name('translation.delivered');
			Route::post('/translation/{translation}/invoice', [TranslationInvoiceController::class, 'create'])->name('translation.invoice.create');
			Route::post('/translation/{translation}/receipt/recreate', [TranslationController::class, 'recreateReceipt'])->name('translation.receipt.recreate');
			Route::get('/prijevod/{translation}/izbrisi', [TranslationController::class, 'delete'])->name('translation.delete');
		});

	});

	// custom translation
	Route::get('/prijevod', [TranslationController::class, 'customCheckout'])->name('translation.custom.checkout');
	Route::post('/translation/process/custom', [TranslationController::class, 'processCustomCheckout'])->name('translation.process.custom');

	// unauthenticated user tutorial routes
	Route::post('/user/wizard/tutorial', [SectionController::class, 'setWizardTutorialShown'])->name('wizard.tutorial');

	// WordPress documents routes
	Route::get('/obrasci', [WordpressController::class, 'documents'])->name('wordpress.documents')->middleware('cacheResponse:wordpress-document');

	// WordPress examples routes
	Route::get('/primjeri', [WordpressController::class, 'examples'])->name('wordpress.examples')->middleware('cacheResponse:wordpress-example');
	Route::get('/primjeri/{slug}', [WordpressController::class, 'example'])->name('wordpress.example')->middleware('cacheResponse:wordpress-example');

	Route::get('/primjeri/{id}/export', [WordpressController::class, 'exportExample'])->name('wordpress.example.export')->middleware(
		'authWithMessage:Prijavite se ili <a href="/register">besplatno napravite račun.</a>,1'
	);

	// WordPress articles routes
	Route::get('/strucni-clanci', [WordpressController::class, 'posts'])->name('wordpress.posts')->middleware('cacheResponse:wordpress-post');
	Route::get('/strucni-clanci/{slug}', [WordpressController::class, 'post'])->name('wordpress.post')->middleware('cacheResponse:wordpress-post');

	// draft preview route
	Route::get('/page/draft', [WordpressController::class, 'pageDraft'])->name('wordpress.page.draft');
	Route::get('/post/draft', [WordpressController::class, 'postDraft'])->name('wordpress.post.draft');

	// template download
	Route::get('/predlozak/{template}', function ($template = null) {

		$path = storage_path() . '/templates/' . $template;
		if (file_exists($path)) {
			return Response::download($path);
		}

		abort(404);
	})->name('template.download');

	// search routes
	Route::post('/template/search', [SearchController::class, 'templates'])->name('search.templates');
	Route::post('/backend/search', [SearchController::class, 'wordpress'])->name('wordpress.search');

	// API routes
	Route::get('/template/tags/sync', [ApiController::class, 'syncTemplateTags'])->name('api.template.tags.sync');
	Route::get('/example/images/delete', [ApiController::class, 'exampleImagesDelete'])->name('api.example.images.delete');
	Route::get('/es/sync', [ApiController::class, 'syncElasticSearch'])->name('api.es.sync');
	Route::get('/cache/clear', [ApiController::class, 'clearCache'])->name('api.cache.clear');

	// throttled routes
	Route::group(['middleware' => ['throttle']], function () {

		// login/registration routes
		Auth::routes(['verify' => true]);

		// SocialAuthController routes
		Route::get('login/{provider}', [SocialAuthController::class, 'redirectToProvider'])->name('auth.social.login');
		Route::get('callback/{provider}', [SocialAuthController::class, 'handleProviderCallback']);

		// contact routes
		Route::get('/kontakt', [ContactController::class, 'index'])->name('contact');
		Route::post('/kontakt', [ContactController::class, 'store'])->name('contact.store')->middleware(\Spatie\Honeypot\ProtectAgainstSpam::class);

	});

	// feedback
	Route::get('/feedback', [FeedbackController::class, 'index'])->name('feedback');
	Route::post('/feedback', [FeedbackController::class, 'store'])->name('feedback.post');
	Route::get('/feedback/facebook', [FeedbackController::class, 'facebook'])->name('feedback.facebook');

	// javascript error reporting
	Route::post('/jserror', function () {

		if(app()->environment('production')) {
			return true; // disable error reporting on production
		}

		$errorData = request()->all();
		$error = json_encode($errorData);

		// List of predefined errors to ignore
		$ignored_errors = [
			"ReferenceError: Can't find variable: gmo",
		];

		// Check if errorMsg matches any ignored error
		if (!empty($errorData['errorMsg'])) {
			foreach ($ignored_errors as $_ignored_error) {
				if (str_contains($errorData['errorMsg'], $_ignored_error)) {
					return false;
				}
			}
		}

		// Check if the error originated from an external script
		$script_url = request()->get('scriptURL');
		$is_external_script = $script_url && !str($script_url)->contains(request()->getHost());
		$is_generic_error = !$script_url;

		// In production, ignore errors from crawlers and external scripts
		if (app()->environment('production')) {
			$cd = new \Jaybizzle\CrawlerDetect\CrawlerDetect();

			if ($cd->isCrawler() || $is_external_script || $is_generic_error) {
				return false;
			}
		}

		// Log or throw exception for other cases
		throw new \Exception("Error in JavaScript: $error");
	});



	// save cookie preferences
	Route::post('/cookiePreferences', [UserController::class, 'setCookiePreferences'])->name('cookie.preferences');
	Route::post('/documentPreviewSetting', [UserController::class, 'documentPreviewSetting'])->name('document.preview.setting');

	// old route redirects
	Route::redirect('/moji-ugovori', '/', 301);
	Route::redirect('/blog/{slug}', '/strucni-clanci/{slug}', 301);

	// other slugs (must be placed at the end of this file)
	Route::get('{slug}', [WordpressController::class, 'page'])->name('wordpress.page')->middleware('cacheResponse:wordpress-page,wordpress-document');
});